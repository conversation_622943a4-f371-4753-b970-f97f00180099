/* pages/inbound/inbound.wxss */

/* ==================== 基础布局 ==================== */
.inbound-container {
  margin-top: 10rpx;
  padding: 0 20rpx 160rpx 20rpx;
  background-color: #f5f5f5;
  height: 100vh;
  overflow: hidden;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
}

.main-content {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  flex: 1;
  min-height: 0;
  overflow: hidden;
}

/* ==================== 货运信息卡片 ==================== */
.transportation-card {
  background: white;
  border-radius: 12rpx;
  padding: 12rpx 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  flex-shrink: 0;
}

.card-content {
  padding: 0;
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr;
  gap: 12rpx;
}

.info-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 6rpx;
}

.label {
  font-size: 22rpx;
  color: #666;
  margin-bottom: 4rpx;
}

.value {
  font-size: 24rpx;
  color: #333;
  font-weight: 500;
  word-break: break-all;
  line-height: 1.2;
}

.highlight {
  color: #1989fa;
  font-weight: bold;
}

/* ==================== 左右分栏布局 ==================== */
.split-layout {
  display: flex;
  gap: 20rpx;
  flex: 1;
  min-height: 0;
  overflow: hidden;
}

.left-panel,
.right-panel {
  background: white;
  border-radius: 16rpx;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.left-panel {
  flex: 0 0 200rpx;
  min-width: 200rpx;
}

.right-panel {
  flex: 1;
}

.panel-header {
  padding: 20rpx 24rpx;
  background: #f8f9fa;
  border-bottom: 1rpx solid #eee;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.panel-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
}

.panel-subtitle {
  font-size: 24rpx;
  color: #666;
  margin-top: 8rpx;
}

/* ==================== 仓库列表 ==================== */
.warehouse-list {
  flex: 1;
  min-height: 0;
  overflow-y: auto;
  overflow-x: hidden;
}

.warehouse-item {
  padding: 20rpx;
  border-bottom: 1rpx solid #eee;
  display: flex;
  align-items: center;
  justify-content: space-between;
  transition: background-color 0.3s;
}

.warehouse-item:active {
  background-color: #f0f0f0;
}

.warehouse-item.selected {
  background-color: #e3f2fd;
  border-left: 4rpx solid #1989fa;
}

.warehouse-info {
  flex: 1;
  cursor: pointer;
}

.warehouse-name {
  font-size: 26rpx;
  font-weight: 600;
  color: #333;
  display: block;
}

.warehouse-address {
  font-size: 22rpx;
  color: #666;
  margin-top: 6rpx;
  display: block;
}

.warehouse-actions {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.edit-icon {
  padding: 8rpx;
  border-radius: 4rpx;
  transition: background-color 0.3s;
}

.edit-icon:active {
  background-color: #f0f0f0;
}

.add-warehouse-btn {
  padding: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  background: #f8f9fa;
  border-top: 1rpx solid #eee;
  color: #1989fa;
  font-size: 26rpx;
  font-weight: 600;
  transition: background-color 0.3s;
}

.add-warehouse-btn:active {
  background-color: #e3f2fd;
}

/* ==================== 到货明细列表 ==================== */
.details-list {
  flex: 1;
  padding: 20rpx;
  min-height: 0;
  width: 100%;
  box-sizing: border-box;
  -ms-overflow-style: none;
  scrollbar-width: none;
  overflow-y: auto;
  overflow-x: hidden;
}

.details-list::-webkit-scrollbar {
  display: none;
}

.detail-item {
  background: white;
  border-radius: 10rpx;
  padding: 12rpx;
  margin-bottom: 12rpx;
  border: 1rpx solid #e9ecef;
  transition: all 0.3s;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.05);
}

.detail-item.completed {
  background: #e8f5e8;
  border-color: #4caf50;
}

.item-content {
  margin-bottom: 0;
}

/* ==================== 服装信息布局 ==================== */
.item-main-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
  padding: 12rpx 14rpx;
  background: #f8f9fa;
  border-radius: 8rpx;
  gap: 16rpx;
}

.clothing-groups {
  flex: 2;
  display: flex;
  flex-direction: column;
  gap: 6rpx;
  min-width: 0;
}

.clothing-group {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 10rpx;
}

.clothing-name {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  line-height: 1.2;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.clothing-name.clickable {
  color: #1989fa;
  cursor: pointer;
  transition: color 0.3s;
}

.clothing-name.clickable:active {
  color: #0c7cd5;
}

.piece-count {
  font-size: 24rpx;
  font-weight: 600;
  color: #1989fa;
  white-space: nowrap;
}

.package-info {
  flex: 0 0 auto;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  min-width: 80rpx;
}

.package-formula {
  font-size: 24rpx;
  font-weight: 600;
  color: #333;
  white-space: nowrap;
}

/* ==================== 操作区域 ==================== */
.item-actions-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 12rpx;
  margin-top: 10rpx;
  padding: 10rpx 14rpx;
  background: #f0f9ff;
  border-radius: 8rpx;
  border: 1rpx solid #e0f2fe;
}

.remaining-info {
  display: flex;
  flex-direction: column;
  justify-content: center;
  text-align: center;
  align-items: center;
  flex-shrink: 0;
  min-width: 60rpx;
}

.remaining-value {
  font-size: 24rpx;
  font-weight: 600;
  color: #ff6b35;
  text-align: center;
}

.remaining-text {
  color: #ff9800;
  font-weight: 600;
  font-size: 30rpx;
  text-align: center;
}

.completed-text {
  color: #4caf50;
  font-weight: 600;
}

.quantity-control {
  display: flex;
  align-items: center;
  justify-content: center;
  flex: 1;
}

.item-tips {
  text-align: center;
  padding: 16rpx;
}

.tip-text {
  font-size: 24rpx;
  color: #999;
}

.completed-tip {
  font-size: 24rpx;
  color: #4caf50;
  font-weight: 600;
}

/* ==================== 底部操作栏 ==================== */
.bottom-fixed-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  border-top: 1rpx solid #eee;
  padding: 20rpx;
  z-index: 1000;
  box-shadow: 0 -4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.bottom-actions {
  display: flex;
  gap: 20rpx;
  justify-content: center;
  align-items: center;
}

.action-item {
  flex: 1;
  max-width: 300rpx;
}

.action-button {
  width: 100% !important;
  border-radius: 12rpx !important;
  height: 80rpx;
  font-size: 28rpx;
  font-weight: 600 !important;
  transition: all 0.3s ease !important;
}

/* 核对按钮特殊样式 */
.action-button[type="success"] {
  background: linear-gradient(135deg, #07c160 0%, #05a050 100%) !important;
  box-shadow: 0 4rpx 12rpx rgba(7, 193, 96, 0.3) !important;
  border: none !important;
  color: white !important;
}

.action-button[type="success"]:active {
  transform: translateY(2rpx) !important;
  box-shadow: 0 2rpx 8rpx rgba(7, 193, 96, 0.4) !important;
}

/* ==================== 待入库清单弹窗 ==================== */

.cart-popup {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: white;
  padding: 0;
  box-sizing: border-box;
  overflow: hidden;
  position: relative;
}

.popup-header {
  padding: 32rpx;
  border-bottom: 1rpx solid #eee;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: white;
  flex-shrink: 0;
  position: relative;
  z-index: 10;
}

.popup-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.total-info {
  font-size: 24rpx;
  color: #666;
}

.cart-content {
  flex: 1;
  padding: 20rpx;
  padding-bottom: 80rpx; /* 增加底部内边距，防止内容被遮挡 */
  min-height: 0;
  width: 100%;
  box-sizing: border-box;
  overflow-y: auto;
  overflow-x: hidden;
  -webkit-overflow-scrolling: touch;
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.cart-content::-webkit-scrollbar {
  display: none;
}

.empty-cart {
  padding: 60rpx 0;
  text-align: center;
}

.warehouse-group {
  margin-bottom: 32rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  overflow: hidden;
}

.warehouse-header {
  background: #e3f2fd;
  padding: 20rpx 24rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.warehouse-summary {
  font-size: 24rpx;
  color: #666;
}

.warehouse-items {
  padding: 0 24rpx;
}


/* ==================== 待入库清单项目 ==================== */

.cart-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #eee;
}

.cart-item:last-child {
  border-bottom: none;
}

.cart-item-content {
  flex: 1;
  margin-right: 20rpx;
}

.cart-main-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8rpx;
  padding: 12rpx 14rpx;
  background: #f8f9fa;
  border-radius: 8rpx;
  gap: 16rpx;
}

/* ==================== 待入库清单项目布局 ==================== */
.cart-clothing-groups {
  flex: 2;
  display: flex;
  flex-direction: column;
  gap: 6rpx;
  min-width: 0;
}

.cart-clothing-group {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 10rpx;
}

.cart-clothing-name {
  font-size: 26rpx;
  font-weight: 600;
  color: #333;
  line-height: 1.2;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.cart-piece-count {
  font-size: 24rpx;
  font-weight: 600;
  color: #1989fa;
  white-space: nowrap;
}

.cart-package-info {
  flex: 0 0 auto;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  min-width: 80rpx;
}

.cart-package-formula {
  font-size: 24rpx;
  font-weight: 600;
  color: #333;
  white-space: nowrap;
}

.cart-item-actions {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

/* ==================== 空状态 ==================== */
.empty-state {
  padding: 60rpx 0;
  text-align: center;
}

/* ==================== 服装信息弹窗 ==================== */






/* 购物车中的服装名称样式 */
.cart-clothing-name.clickable {
  color: #1989fa;
  cursor: pointer;
  transition: color 0.3s;
}

.cart-clothing-name.clickable:active {
  color: #0c7cd5;
}
